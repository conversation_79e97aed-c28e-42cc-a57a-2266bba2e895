package com.example.splitexpenses.ui.components


import androidx.compose.animation.*
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ListItem
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.example.splitexpenses.R
import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.ui.components.ExportDialog
import com.example.splitexpenses.ui.components.ExpenseFilterMenu
import com.example.splitexpenses.ui.components.OfflineStatusIndicator
import com.example.splitexpenses.ui.viewmodels.ExpenseFilterState
import com.example.splitexpenses.ui.viewmodels.applyFilters
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.foundation.layout.Arrangement.spacedBy
import androidx.compose.ui.graphics.Color
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically

import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.SizeTransform
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import java.io.OutputStream
import androidx.compose.animation.slideIn
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOut
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.with
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.AlertDialog
import androidx.compose.ui.draw.clip
import androidx.compose.ui.modifier.modifierLocalConsumer
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.sp


@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ExpenseListScreen(
    group: GroupData,
    onExpenseClick: (Expense) -> Unit,
    onShowBalanceDetailsClick: () -> Unit,
    onAddExpenseClick: () -> Unit,
    onDeleteExpense: (Set<String>) -> Unit,
    onShowStatisticsClick: () -> Unit,
    onShowManageMembersClick: () -> Unit,
    onShowDeleteGroupDialog: () -> Unit,
    onExportToCsv: (OutputStream) -> Boolean,
    isMultiSelectMode: Boolean,
    onMultiSelectModeChange: (Boolean) -> Unit,
    selectedExpenses: Set<String>,
    onSelectedExpensesChange: (String, Boolean) -> Unit,
    onNavigateToManageCategories: () -> Unit,
    isCurrentUserGroupCreator: Boolean = false,
    onEditGroupName: () -> Unit = {},
    isOffline: Boolean = false,
    filterState: ExpenseFilterState = ExpenseFilterState(),
    onFilterStateChange: (ExpenseFilterState) -> Unit = {}
) {
    var showExportDialog by remember { mutableStateOf(false) }
    var showDeleteConfirmationDialog by remember { mutableStateOf(false) }
    var showFilterMenu by remember { mutableStateOf(false) }

    // Handle back button specifically within this screen
    BackHandler(enabled = isMultiSelectMode || showFilterMenu) {
        when {
            showFilterMenu -> showFilterMenu = false
            isMultiSelectMode -> onMultiSelectModeChange(false)
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
        ) {
            // Group header row with menu - Enhanced design
            var showGroupMenu by remember { mutableStateOf(false) }

            ElevatedCard(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                elevation = CardDefaults.elevatedCardElevation(defaultElevation = 2.dp),
                colors = CardDefaults.elevatedCardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = group.name,
                            style = MaterialTheme.typography.headlineMedium,
                            color = MaterialTheme.colorScheme.primary,
                            maxLines = 1
                        )
                        Text(
                            text = "${group.members.size} members • ${group.expenses.size} expenses",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }

                    val currentState = if (isMultiSelectMode) {
                        Icons.Default.Delete
                    } else {
                        Icons.Default.MoreVert
                    }

                    Box(
                        contentAlignment = Alignment.Center
                    ) {
                        // Animate icon changes with directional sliding
                        AnimatedContent(
                            targetState = currentState,
                            transitionSpec = {
                                val transition = if (isMultiSelectMode) {
                                    slideInHorizontally(initialOffsetX = { fullWidth -> fullWidth }) + fadeIn() togetherWith
                                            slideOutHorizontally(targetOffsetX = { fullWidth -> fullWidth }) + fadeOut()
                                } else {
                                    slideInHorizontally(initialOffsetX = { fullWidth -> fullWidth }) + fadeIn() togetherWith
                                            slideOutHorizontally(targetOffsetX = { fullWidth -> fullWidth }) + fadeOut()
                                }
                                transition.using(SizeTransform(clip = false))
                            },
                            label = "iconAnimation"
                        ) { icon ->
                            IconButton(
                                onClick = {
                                    if (isMultiSelectMode) {
                                        if (!isOffline) {
                                            showDeleteConfirmationDialog = true
                                        }
                                    } else {
                                        showGroupMenu = true
                                    }
                                },
                                enabled = !isMultiSelectMode || !isOffline
                            ) {
                                Icon(
                                    imageVector = icon,
                                    contentDescription = null,
                                    tint = if (isMultiSelectMode) {
                                        MaterialTheme.colorScheme.error
                                    } else {
                                        MaterialTheme.colorScheme.onSurfaceVariant
                                    }
                                )
                            }
                        }
                    }
                }
            }

            // Dropdown menu positioned outside the card
            Box {
                DropdownMenu(
                    expanded = showGroupMenu,
                    onDismissRequest = { showGroupMenu = false },
                    containerColor = MaterialTheme.colorScheme.surface,
                    tonalElevation = 8.dp,
                    modifier = Modifier.animateContentSize(
                        animationSpec = tween(
                            durationMillis = 300,
                            easing = FastOutSlowInEasing
                        )
                    ),
                ) {
                    DropdownMenuItem(
                        text = { Text("Statistics") },
                        onClick = {
                            showGroupMenu = false
                            onShowStatisticsClick()
                        },
                        leadingIcon = {
                            Icon(
                                painter = painterResource(id = R.drawable.chart_arc),
                                contentDescription = "Statistics"
                            )
                        }
                    )

                    DropdownMenuItem(
                        text = {
                            Text(
                                "Manage Categories",
                                color = if (isOffline) MaterialTheme.colorScheme.onSurface.copy(
                                    alpha = 0.38f
                                ) else MaterialTheme.colorScheme.onSurface
                            )
                        },
                        onClick = {
                            if (!isOffline) {
                                showGroupMenu = false
                                onNavigateToManageCategories()
                            }
                        },
                        enabled = !isOffline,
                        leadingIcon = {
                            Icon(
                                painter = painterResource(id = R.drawable.tag_outline),
                                contentDescription = "Manage Categories",
                                tint = if (isOffline) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f) else MaterialTheme.colorScheme.onSurface
                            )
                        }
                    )

                    DropdownMenuItem(
                        text = {
                            Text(
                                "Manage Group",
                                color = if (isOffline) MaterialTheme.colorScheme.onSurface.copy(
                                    alpha = 0.38f
                                ) else MaterialTheme.colorScheme.onSurface
                            )
                        },
                        onClick = {
                            if (!isOffline) {
                                showGroupMenu = false
                                println("ExpenseListScreen: Clicked Manage Members")
                                onShowManageMembersClick()
                            }
                        },
                        enabled = !isOffline,
                        leadingIcon = {
                            Icon(
                                painter = painterResource(id = R.drawable.account_outline),
                                contentDescription = "Manage Members",
                                tint = if (isOffline) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f) else MaterialTheme.colorScheme.onSurface
                            )
                        }
                    )

                    DropdownMenuItem(
                        text = { Text("Export to CSV") },
                        onClick = {
                            showGroupMenu = false
                            showExportDialog = true
                        },
                        leadingIcon = {
                            Icon(
                                painter = painterResource(id = R.drawable.export),
                                contentDescription = "Export"
                            )
                        }
                    )

                    DropdownMenuItem(
                        text = {
                            Text(
                                "Delete Group",
                                color = if (isOffline) MaterialTheme.colorScheme.onSurface.copy(
                                    alpha = 0.38f
                                ) else MaterialTheme.colorScheme.error
                            )
                        },
                        onClick = {
                            if (!isOffline) {
                                showGroupMenu = false
                                onShowDeleteGroupDialog()
                            }
                        },
                        enabled = !isOffline,
                        leadingIcon = {
                            Icon(
                                Icons.Default.Delete,
                                contentDescription = "Delete",
                                tint = if (isOffline) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f) else MaterialTheme.colorScheme.error
                            )
                        }
                    )
                }
            }
        }

        // Show offline status indicator if offline
        AnimatedVisibility(
            visible = isOffline,
            enter = slideInVertically() + fadeIn(),
            exit = slideOutVertically() + fadeOut()
        ) {
            OfflineStatusIndicator(
                isOffline = true,
                modifier = Modifier.padding(vertical = 8.dp)
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Enhanced Balances summary with filter icon
        ElevatedCard(
            modifier = Modifier
                .fillMaxWidth()
                .clickable(onClick = onShowBalanceDetailsClick),
            elevation = CardDefaults.elevatedCardElevation(defaultElevation = 1.dp),
            colors = CardDefaults.elevatedCardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    // Apply filters to get filtered total
                    val filteredExpenses = group.expenses.applyFilters(filterState)
                    val totalSpent = filteredExpenses.sumOf { it.amount }

                    // Animate the total spent value
                    val animatedTotalSpent = animateFloatAsState(
                        targetValue = totalSpent.toFloat(),
                        animationSpec = spring(
                            dampingRatio = Spring.DampingRatioMediumBouncy,
                            stiffness = Spring.StiffnessLow
                        ),
                        label = "totalSpent"
                    )

                    Text(
                        text = "Total spent",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "${String.format("%.2f", animatedTotalSpent.value)}€",
                        style = MaterialTheme.typography.headlineLarge,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = if (filterState.hasActiveFilters()) {
                            "Showing ${filteredExpenses.size} of ${group.expenses.size} expenses • Tap for breakdown"
                        } else {
                            "Tap for detailed breakdown"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // Enhanced Filter icon button with badge
                Box {
                    Surface(
                        modifier = Modifier
                            .size(56.dp)
                            .combinedClickable(
                                onClick = { showFilterMenu = true },
                                onLongClick = {
                                    // Clear all filters on long press
                                    onFilterStateChange(ExpenseFilterState())
                                }
                            ),
                        shape = CircleShape,
                        color = if (filterState.hasActiveFilters()) {
                            MaterialTheme.colorScheme.primaryContainer
                        } else {
                            MaterialTheme.colorScheme.surface
                        },
                        tonalElevation = if (filterState.hasActiveFilters()) 4.dp else 1.dp
                    ) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                painter = if (filterState.hasActiveFilters()) {
                                    painterResource(id = R.drawable.filter)
                                } else {
                                    painterResource(id = R.drawable.filter_outline)
                                },
                                contentDescription = "Filter expenses • Long press to clear filters",
                                tint = if (filterState.hasActiveFilters()) {
                                    MaterialTheme.colorScheme.onPrimaryContainer
                                } else {
                                    MaterialTheme.colorScheme.onSurfaceVariant
                                }
                            )
                        }
                    }

                    // Animated circular badge showing filter count
                    AnimatedVisibility(
                        visible = filterState.hasActiveFilters(),
                        enter = slideInVertically() + fadeIn(),
                        exit = slideOutVertically() + fadeOut()
                    ) {
                        Surface(
                            modifier = Modifier
                                .size(20.dp)
                                .align(Alignment.TopEnd)
                                .padding(top = 2.dp, end = 2.dp),
                            shape = CircleShape,
                            color = MaterialTheme.colorScheme.error
                        ) {
                            Box(
                                modifier = Modifier.fillMaxSize(),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "${filterState.getActiveFilterCount()}",
                                    style = MaterialTheme.typography.labelSmall,
                                    color = MaterialTheme.colorScheme.onError,
                                    fontSize = 10.sp
                                )
                            }
                        }
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Enhanced Expenses list
        val dateFormat = remember { SimpleDateFormat("dd/MM/yyyy", Locale.getDefault()) }

        // Apply filters and sort expenses by date (most recent first)
        val filteredExpenses = group.expenses.applyFilters(filterState)
        val sortedExpenses = filteredExpenses.sortedByDescending { it.date }

        // Group expenses by date
        val expensesByDate = sortedExpenses.groupBy { expense ->
            // Extract the date part only (without time) for grouping
            val calendar = Calendar.getInstance()
            calendar.timeInMillis = expense.date
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            calendar.timeInMillis
        }

        // Convert to list and sort by date (most recent first)
        val groupedExpenses = expensesByDate.entries
            .sortedByDescending { it.key } // Sort by date timestamp (most recent first)
            .map { (dateTimestamp, expenses) ->
                // Format the date for display
                val formattedDate = dateFormat.format(Date(dateTimestamp))
                formattedDate to expenses
            }

        // Show empty state if no expenses match filters
        if (filteredExpenses.isEmpty()) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = if (filterState.hasActiveFilters()) {
                        "No expenses match your filters"
                    } else {
                        "No expenses yet"
                    },
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = androidx.compose.ui.text.style.TextAlign.Center
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = if (filterState.hasActiveFilters()) {
                        "Try adjusting your filters or long press the filter button to clear all filters"
                    } else {
                        "Tap the + button to add your first expense"
                    },
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = androidx.compose.ui.text.style.TextAlign.Center
                )
            }
        } else {
            LazyColumn(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = spacedBy(4.dp)
            ) {
                groupedExpenses.forEach { (date, expenses) ->
                    item {
                        // Enhanced date header
                        Surface(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp),
                            shape = MaterialTheme.shapes.small,
                            color = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
                        ) {
                            Text(
                                text = date,
                                style = MaterialTheme.typography.titleMedium,
                                color = MaterialTheme.colorScheme.onSecondaryContainer,
                                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                            )
                        }
                    }

                    val sortedDayExpenses = expenses.sortedByDescending { it.date }
                    itemsIndexed(sortedDayExpenses) { index, expense ->
                        // Enhanced expense card with better animations
                        val isSelected = expense.id in selectedExpenses

                        // Animate selection state
                        val cardElevation = animateFloatAsState(
                            targetValue = if (isSelected) 8f else 2f,
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioMediumBouncy,
                                stiffness = Spring.StiffnessMedium
                            ),
                            label = "cardElevation"
                        )

                        val cardScale = animateFloatAsState(
                            targetValue = if (isSelected) 0.98f else 1f,
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioMediumBouncy,
                                stiffness = Spring.StiffnessMedium
                            ),
                            label = "cardScale"
                        )

                        ElevatedCard(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 4.dp, vertical = 2.dp)
                                .graphicsLayer {
                                    scaleX = cardScale.value
                                    scaleY = cardScale.value
                                }
                                .combinedClickable(
                                    onClick = {
                                        if (isMultiSelectMode) {
                                            if (!isOffline) {
                                                onSelectedExpensesChange(
                                                    expense.id,
                                                    expense.id !in selectedExpenses
                                                )
                                            }
                                        } else {
                                            // Allow viewing expense details even when offline
                                            onExpenseClick(expense)
                                        }
                                    },
                                    onLongClick = {
                                        if (!isMultiSelectMode && !isOffline) {
                                            onMultiSelectModeChange(true)
                                            onSelectedExpensesChange(expense.id, true)
                                        }
                                    }
                                ),
                            elevation = CardDefaults.elevatedCardElevation(
                                defaultElevation = cardElevation.value.dp
                            ),
                            colors = CardDefaults.elevatedCardColors(
                                containerColor = if (isSelected) {
                                    MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                                } else {
                                    MaterialTheme.colorScheme.surface
                                }
                            ),
                            border = if (isSelected) {
                                BorderStroke(2.dp, MaterialTheme.colorScheme.primary)
                            } else null
                        ) {
                            // Enhanced card content with better layout
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp)
                            ) {
                                // Top row: emoji + description and amount
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Row(
                                        modifier = Modifier.weight(1f),
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = spacedBy(12.dp)
                                    ) {
                                        // Enhanced emoji display
                                        Surface(
                                            modifier = Modifier.size(40.dp),
                                            shape = CircleShape,
                                            color = MaterialTheme.colorScheme.primaryContainer.copy(
                                                alpha = 0.3f
                                            )
                                        ) {
                                            Box(
                                                modifier = Modifier.fillMaxSize(),
                                                contentAlignment = Alignment.Center
                                            ) {
                                                Text(
                                                    text = group.categories.find { it.name == expense.category }?.emoji
                                                        ?: "💰",
                                                    style = MaterialTheme.typography.titleLarge
                                                )
                                            }
                                        }

                                        Text(
                                            text = expense.description,
                                            style = MaterialTheme.typography.titleMedium,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            maxLines = 1,
                                            overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis
                                        )
                                    }

                                    // Animate the expense amount with spring animation
                                    val animatedAmount = animateFloatAsState(
                                        targetValue = expense.amount.toFloat(),
                                        animationSpec = spring(
                                            dampingRatio = Spring.DampingRatioMediumBouncy,
                                            stiffness = Spring.StiffnessLow
                                        ),
                                        label = "expenseAmount"
                                    )
                                    Text(
                                        text = "%.2f€".format(animatedAmount.value),
                                        style = MaterialTheme.typography.headlineSmall,
                                        color = MaterialTheme.colorScheme.primary,
                                        fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                                    )
                                }

                                Spacer(modifier = Modifier.height(8.dp))

                                // Bottom row: category and paid by
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Surface(
                                        shape = MaterialTheme.shapes.small,
                                        color = MaterialTheme.colorScheme.secondaryContainer.copy(
                                            alpha = 0.5f
                                        )
                                    ) {
                                        Text(
                                            text = expense.category,
                                            color = MaterialTheme.colorScheme.onSecondaryContainer,
                                            style = MaterialTheme.typography.bodySmall,
                                            modifier = Modifier.padding(
                                                horizontal = 8.dp,
                                                vertical = 4.dp
                                            )
                                        )
                                    }

                                    Text(
                                        text = "Paid by ${expense.paidBy}",
                                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                                        style = MaterialTheme.typography.bodySmall,
                                        fontStyle = FontStyle.Italic
                                    )
                                }
                            }
                        }
                    }
                }
                // Add padding at the bottom for FAB
                item {
                    Spacer(modifier = Modifier.height(80.dp))
                }
            }
        }
    }


    // FAB for adding expenses
    Box(
        modifier = Modifier
            .padding(16.dp)
            .align(Alignment.BottomEnd)
    ) {
        FloatingActionButton(
            onClick = onAddExpenseClick
        ) {
            Icon(Icons.Default.Add, contentDescription = "Add Expense")
        }
    }

    // No category management dialog anymore - using screen navigation instead

    // Export dialog
    if (showExportDialog) {
        ExportDialog(
            groupName = group.name,
            onDismiss = { showExportDialog = false },
            onExport = onExportToCsv
        )
    }

    // Delete confirmation dialog
    if (showDeleteConfirmationDialog) {
        DeleteMultipleExpensesDialog(
            expenseCount = selectedExpenses.size,
            onDismiss = { showDeleteConfirmationDialog = false },
            onConfirm = {
                onDeleteExpense(selectedExpenses)
                onMultiSelectModeChange(false)
                showDeleteConfirmationDialog = false
            }
        )
    }

    // Filter menu
    ExpenseFilterMenu(
        isVisible = showFilterMenu,
        filterState = filterState,
        availableCategories = group.categories,
        availableMembers = group.members,
        allExpenses = group.expenses,
        onFilterStateChange = onFilterStateChange,
        onDismiss = { showFilterMenu = false }
    )
}

